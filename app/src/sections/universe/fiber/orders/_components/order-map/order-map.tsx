import { useEffect, useState, useMemo } from 'react';
import Map, { <PERSON><PERSON>, <PERSON><PERSON>, NavigationControl } from 'react-map-gl/mapbox';
// api
import type { IFiberEndpointAddress } from '@/api/interface/fiber';
import { useOrder } from '@/hooks/queries/use-order';
import { MapPin1Icon } from '@/components-old/ui/icon';
import 'mapbox-gl/dist/mapbox-gl.css';
import { useTheme } from '@/context/theme-context.tsx';
import AddressInfo from '@/sections/universe/fiber/orders/_components/order-map/address-info.tsx';
import { Badge, type TBadgeVariant } from '@/components/ui/badge.tsx';
import { Expand05 } from '@tools/reactor-icons';
import { FullScreen, useFullScreenHandle } from 'react-full-screen';
import { Button } from '@/components/ui/button.tsx';

// ----------------------------------------------------------------------

type TViewState = {
	longitude: number;
	latitude: number;
	zoom?: number;
};

type Props = {
	reference?: string;
};

const PROVIDER_TAGS_MAPPING: Record<string, { label: string; variant: TBadgeVariant }> = {
	DS: { label: 'PBO Souterrain', variant: 'solid-black' },
	DA: { label: 'PBO Aérien', variant: 'solid-black' },
	DF: { label: 'PBO Facade', variant: 'solid-black' },
	DC: { label: 'PBO Chambre', variant: 'solid-black' },
	DE: { label: 'PBO Égout', variant: 'solid-black' },
	NEUF: { label: 'Bâtiment neuf', variant: 'solid-black' },
	PM: { label: "PM à l'adresse", variant: 'solid-black' },
	NRO: { label: "NRO à l'adresse", variant: 'solid-black' },
	PRIVE: { label: 'Accès par accord tiers', variant: 'solid-black' },
	BANQUE: { label: 'Accès réglementé', variant: 'solid-dark-red' },
	FREECENTER: { label: 'Boutique Free', variant: 'solid-black' },
	PROBLEME: { label: "Problème à l'adresse", variant: 'solid-dark-red' },
	COMMERCE: { label: 'Zone commerciale', variant: 'solid-black' },
	SPECIAL: { label: 'Spécifique Free', variant: 'solid-dark-red' }
};
export default function OrderMap({ reference }: Props) {
	const [viewState, setViewState] = useState<TViewState>();
	const [popupInfo, setPopupInfo] = useState<IFiberEndpointAddress | null>(null);
	const { theme } = useTheme();
	const { order, queryOrder, adel } = useOrder(reference);

	const handle = useFullScreenHandle();

	const hasMultipleTypologies = useMemo(() => {
		if (!adel?._source?.adel?.providers || adel._source.adel.providers.length <= 1) {
			return false;
		}

		const techValues = new Set();
		adel._source.adel.providers.forEach((provider: any) => {
			if (provider.tech) {
				techValues.add(provider.tech);
			}
		});

		return techValues.size > 1;
	}, [adel]);

	const renderProviderTagsBadges = () => {
		const providersTags = adel?._source?.adel?.providersTags;
		if (!providersTags || !Array.isArray(providersTags) || providersTags.length === 0) {
			return null;
		}

		return providersTags
			.map((tag: string, index: number) => {
				const tagConfig = PROVIDER_TAGS_MAPPING[tag];
				if (!tagConfig) {
					return null;
				}

				return (
					<Badge key={`${tag}-${index}`} variant={tagConfig.variant}>
						{tagConfig.label}
					</Badge>
				);
			})
			.filter(Boolean);
	};

	const { isSuccess: orderIsSuccess } = queryOrder;
	useEffect(() => {
		if (orderIsSuccess) {
			const longitude = parseFloat(order?.endpointAddress?.longitude || '0');
			const latitude = parseFloat(order?.endpointAddress?.latitude || '0');

			setViewState({
				longitude,
				latitude,
				zoom: 15
			});
		}
	}, [orderIsSuccess]);

	useEffect(() => {
		console.log('handle', handle.active);
	}, [handle.active]);

	return (
		<section className='bg-gradient-to-b from-yellow-500 to-white p-0.5 rounded-xl'>
			<div className='relative bg-white rounded-xl dark:bg-free-dark-smoke'>
				<div className='relative'>
					<div
						className='absolute z-10 pointer-events-none size-full rounded-t-xl'
						style={{
							boxShadow: `inset 0 -17px 27px -17px #00000024`
						}}
					/>
					<FullScreen handle={handle}>
						<Map
							{...viewState}
							mapboxAccessToken={import.meta.env.VITE_MAPBOX_ACCESS_TOKEN}
							style={{
								height: handle.active ? '100vh' : 300,
								width: '100%',
								borderRadius: '10px 10px 0 0',
								border: 'none'
							}}
							initialViewState={{ zoom: 15 }}
							minZoom={10}
							maxZoom={18}
							onMove={(evt) => setViewState(evt.viewState)}
							mapStyle={theme === 'dark' ? 'mapbox://styles/mapbox/dark-v11' : 'mapbox://styles/mapbox/light-v11'}>
							<NavigationControl />
							<Marker
								longitude={parseFloat(order?.endpointAddress?.longitude || '0')}
								latitude={parseFloat(order?.endpointAddress?.latitude || '0')}
								anchor='bottom'
								onClick={(e) => {
									e.originalEvent.stopPropagation();
									if (order?.endpointAddress) {
										setPopupInfo(order.endpointAddress);
									}
								}}>
								<MapPin1Icon />
							</Marker>
							{popupInfo && (
								<Popup
									anchor='top'
									longitude={popupInfo?.longitude ? Number(popupInfo.longitude) : 0}
									latitude={popupInfo?.latitude ? Number(popupInfo.latitude) : 0}
									onClose={() => setPopupInfo(null)}>
									<p>
										{popupInfo.street}, {popupInfo.street2 ? `${popupInfo.street2},` : ''} {popupInfo.postalCode}{' '}
										{popupInfo.city}
									</p>
								</Popup>
							)}
							<div className='absolute bottom-2 left-2 z-10 flex gap-1'>
								{renderProviderTagsBadges()}
								{hasMultipleTypologies && <Badge variant='solid-black'>Multi-typo</Badge>}
							</div>
							<div className='absolute top-2 right-2 z-10 flex flex-col gap-1'>
								<Button
									className='h-9 w-9 rounded-full bg-black/90 text-white flex items-center justify-center cursor-pointer'
									onClick={handle.active ? handle.exit : handle.enter}>
									<Expand05 className='h-5 w-5' />
								</Button>
								<a
									target={'_blank'}
									href={`https://www.google.com/maps?q=${order?.endpointAddress?.latitude},${order?.endpointAddress?.longitude}`}
									rel='noreferrer'>
									<svg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'>
										<circle cx='18' cy='18' r='18' fill='#171717' />
										<g clip-path='url(#clip0_3487_101)'>
											<path
												d='M20.1311 8.34921C19.4638 8.12698 18.7357 8 17.9925 8C15.8538 8 13.9275 9.01587 12.6382 10.619L15.9448 13.5238L20.1311 8.34921Z'
												fill='#1A73E8'
											/>
											<path
												d='M12.6381 10.6191C11.6219 11.889 11 13.5398 11 15.3176C11 16.6985 11.2579 17.8096 11.6977 18.8096L15.9447 13.5239L12.6381 10.6191Z'
												fill='#EA4335'
											/>
											<path
												d='M18.0077 12.5237C19.4941 12.5237 20.6924 13.7777 20.6924 15.3332C20.6924 16.0158 20.4497 16.6507 20.0553 17.1428C20.0553 17.1428 22.1637 14.5079 24.2265 11.9523C23.3771 10.238 21.9058 8.93642 20.1312 8.34912L15.9448 13.5237C16.4454 12.9205 17.1734 12.5237 18.0077 12.5237Z'
												fill='#4285F4'
											/>
											<path
												d='M18.0076 18.1271C16.5212 18.1271 15.3229 16.8731 15.3229 15.3176C15.3229 14.635 15.5504 14.0001 15.9448 13.5239L11.6978 18.8096C12.4258 20.4922 13.6392 21.8573 14.883 23.5557L20.0553 17.1271C19.5547 17.7461 18.8267 18.1271 18.0076 18.1271Z'
												fill='#FBBC04'
											/>
											<path
												d='M19.9643 25.3331C22.3002 21.5077 25.0152 19.7775 25.0152 15.3331C25.0152 14.1109 24.727 12.968 24.2265 11.9521L14.8831 23.5553C15.2774 24.095 15.687 24.7141 16.0813 25.349C17.5071 27.6506 17.1127 29.0156 18.0228 29.0156C18.9329 29.0156 18.5385 27.6347 19.9643 25.3331Z'
												fill='#34A853'
											/>
										</g>
										<defs>
											<clipPath id='clip0_3487_101'>
												<rect width='14' height='21' fill='white' transform='translate(11 8)' />
											</clipPath>
										</defs>
									</svg>
								</a>
							</div>
						</Map>
					</FullScreen>
				</div>
				<div className='flex p-2 text-sm'>
					<AddressInfo />
				</div>
			</div>
		</section>
	);
}
