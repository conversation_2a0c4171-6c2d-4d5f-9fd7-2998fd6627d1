// types
import type { TBaseModalProps } from '@/types/modal.interface';

// ----------------------------------------------------------------------

export interface EditOrderModalProps extends TBaseModalProps {}

export interface EditOrderFormData {
	// Tab 1: Référentiel identification
	jetonCommande: string;
	entrepriseCliente: string;
	retailer: string;
	referenceBDC: string;
	referenceEP: string;
	referenceJFP: string;
	entrepriseDistributeur: string;

	// Tab 2: Référentiel géographique
	iwAdresseId: string;
	latitude: string;
	longitude: string;
	complementAdresse: string;
	batimentIMB: string;
	referenceAPCBatiment: string;
	referenceAPCEscalier: string;
	referenceAPCEtage: string;

	// Tab 3: Référentiel optique
	commandeMultiAcces: boolean;
	autoriserMigrationB2CB2B: boolean;
	pm: string;
	ptoARaccorder: string;
	ptoAConserver: string;
	referenceClientB2C: string;
	pmt: string;

	// Tab 4: Offre commerciale optique
	provenance: string;
	venteIndirecteCanal: string;
	garantiesSAV: string;
	typeOffre: string;
	// Options
	freeboxPro1: boolean;
	freeboxPro2: boolean;
	kitMiseEnBaie1U: boolean;
	comsProUCaaS: boolean;
	supportPremium: boolean;
	disqueDurNVMe1To: boolean;
	backup4G: boolean;
	repeteurWIFI: boolean;
	repeteurWIFIQuantity: number;

	// Tab 5: Autre
	commandeTest: boolean;
	modeAutomatique: boolean;
	vip: boolean;
}

export type SelectOption = {
	label: string;
	value: string;
};

export const PROVENANCE_OPTIONS: SelectOption[] = [
	{ label: 'Inconnu', value: 'unknown' },
	{ label: 'Web', value: 'web' },
	{ label: 'Téléphone', value: 'phone' },
	{ label: 'Boutique', value: 'shop' },
	{ label: 'Commerciaux', value: 'sales' },
	{ label: 'Simbox', value: 'simbox' }
];

export const VENTE_INDIRECTE_CANAL_OPTIONS: SelectOption[] = [
	{ label: 'PredictivPro', value: 'predictivpro' },
	{ label: 'Boutique Free', value: 'boutique_free' },
	{ label: 'PME', value: 'pme' }
];

export const GARANTIES_SAV_OPTIONS: SelectOption[] = [
	{ label: 'GTR 4h', value: 'GTR4' },
	{ label: 'GTI 4h', value: 'GTI4' },
	{ label: 'GTR 8h', value: 'GTR8' },
	{ label: 'GTI 8h', value: 'GTI8' }
];

export const TYPE_OFFRE_OPTIONS: SelectOption[] = [
	{ label: 'FTTO', value: 'ftto' },
	{ label: 'FTTH', value: 'ftth' }
];
