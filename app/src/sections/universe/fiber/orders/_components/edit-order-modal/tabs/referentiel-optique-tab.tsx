// utils
import { useFormContext } from 'react-hook-form';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { FormLabelRequired } from '@/components/ui/form-label-required';
import { Input } from '@/components/ui/input';
import { SwitchCard } from '@/components/ui/switch-card';
// types
import type { EditOrderFormData } from '../schema';

// ----------------------------------------------------------------------

export default function ReferentielOptiqueTab() {
	const { control } = useFormContext<EditOrderFormData>();

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-2 gap-4'>
				<SwitchCard
					name='commandeMultiAcces'
					label='Commande multi-accès'
					activeLabel='Demandé'
					inactiveLabel='Non demandé'
					badgeVariant='green'
				/>

				<FormField
					control={control}
					name='ptoAConserver'
					render={({ field }) => (
						<FormItem>
							<FormLabelRequired required>PTO à conserver</FormLabelRequired>
							<FormControl>
								<Input placeholder='PTO à conserver' {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Separator */}
			<div className='border-t border-border'></div>

			<div className='grid grid-cols-2 gap-4'>
				<SwitchCard
					name='autoriserMigrationB2CB2B'
					label='Autoriser la migration B2C - B2B'
					activeLabel='Actif'
					inactiveLabel='Inactif'
					badgeVariant='green'
				/>

				<FormField
					control={control}
					name='referenceClientB2C'
					render={({ field }) => (
						<FormItem>
							<FormLabel>Référence client B2C</FormLabel>
							<FormControl>
								<Input placeholder='Référence client B2C' {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Separator */}
			<div className='border-t border-border'></div>

			<div className='grid grid-cols-2 gap-4'>
				<FormField
					control={control}
					name='pm'
					render={({ field }) => (
						<FormItem>
							<FormLabel>PM</FormLabel>
							<FormControl>
								<Input placeholder='PM' {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={control}
					name='pmt'
					render={({ field }) => (
						<FormItem>
							<FormLabel>PMT</FormLabel>
							<FormControl>
								<Input placeholder='PMT' {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<FormField
				control={control}
				name='ptoARaccorder'
				render={({ field }) => (
					<FormItem>
						<FormLabel>PTO à raccorder</FormLabel>
						<FormControl>
							<Input placeholder='PTO à raccorder' {...field} />
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
}
