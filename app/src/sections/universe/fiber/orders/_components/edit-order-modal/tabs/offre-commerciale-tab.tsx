// utils
import { useFormContext } from 'react-hook-form';
import { Minus, Plus, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckboxCard } from '@/components/ui/checkbox-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// types
import type { EditOrderFormData } from '../schema';
import { PROVENANCE_OPTIONS, VENTE_INDIRECTE_CANAL_OPTIONS, GARANTIES_SAV_OPTIONS, TYPE_OFFRE_OPTIONS } from '../types';
import { RIcon } from '@tools/reactor-icons';

// ----------------------------------------------------------------------

export default function OffreCommercialeTab() {
	const { control, watch, setValue } = useFormContext<EditOrderFormData>();

	const repeteurWIFIQuantity = watch('repeteurWIFIQuantity');

	const handleQuantityChange = (increment: boolean) => {
		const currentValue = repeteurWIFIQuantity || 0;
		const newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);
		setValue('repeteurWIFIQuantity', newValue);
	};

	const handleCheckboxToggle = () => {
		const newValue = repeteurWIFIQuantity > 0 ? 0 : 1;
		setValue('repeteurWIFIQuantity', newValue);
	};

	const handleQuantityInputChange = (value: number) => {
		setValue('repeteurWIFIQuantity', Math.max(0, value));
	};

	return (
		<div className='space-y-6'>
			{/* Selects section */}
			<div className='grid grid-cols-2 gap-4'>
				{/* Column 1 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='provenance'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Provenance</FormLabel>
								<Select onValueChange={field.onChange} defaultValue={field.value}>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder='Sélectionner une provenance' />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{PROVENANCE_OPTIONS.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='venteIndirecteCanal'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Vente indirecte : Canal de vente</FormLabel>
								<Select onValueChange={field.onChange} defaultValue={field.value}>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder='Sélectionner un canal' />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{VENTE_INDIRECTE_CANAL_OPTIONS.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Column 2 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='garantiesSAV'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Garanties SAV</FormLabel>
								<Select onValueChange={field.onChange} defaultValue={field.value}>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder='Sélectionner une garantie' />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{GARANTIES_SAV_OPTIONS.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='typeOffre'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Type d'offre</FormLabel>
								<Select onValueChange={field.onChange} defaultValue={field.value}>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder='Sélectionner un type' />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{TYPE_OFFRE_OPTIONS.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Options section */}
			<div>
				<h4 className='text-sm font-medium mb-4'>Options</h4>
				<div className='grid grid-cols-3 gap-4'>
					<CheckboxCard name='freeboxPro1' label='Freebox Pro 1' iconName='Copy07' />

					<CheckboxCard name='freeboxPro2' label='Freebox Pro 2' iconName='NotificationBox' />

					<CheckboxCard name='kitMiseEnBaie1U' label='Kit de Mise en baie 1U' iconName='Keyboard02' />

					<CheckboxCard name='comsProUCaaS' label='Coms Pro (UCaaS)' iconName='MessageChatCircle' />

					<CheckboxCard name='supportPremium' label='Support premium' iconName='Diamond02' />

					<CheckboxCard name='disqueDurNVMe1To' label='Disque Dur NVMe 1To' iconName='HardDrive' />

					<CheckboxCard name='backup4G' label='Backup 4G' iconName='Signal01' />

					{/* Répéteur WIFI with quantity - spans 2 columns */}
					<div className='col-span-2'>
						{/* biome-ignore lint/a11y/useKeyWithClickEvents: Il veux des key up et key down qui servent à r, faut que je delete la rule */}
						<div
							className={cn(
								'flex flex-row items-center space-x-3 space-y-0 rounded-xl border py-4 px-5 cursor-pointer transition-colors hover:bg-accent/20 w-full justify-between',
								repeteurWIFIQuantity > 0 && 'bg-purple-50 border-primary hover:bg-purple-100'
							)}
							onClick={handleCheckboxToggle}>
							<div className='flex items-center gap-3'>
								<div
									className={cn(
										'h-4 w-4 shrink-0 rounded-sm border border-primary shadow flex items-center justify-center',
										repeteurWIFIQuantity > 0 && 'bg-primary text-primary-foreground'
									)}>
									{repeteurWIFIQuantity > 0 && <Check className='h-4 w-4' />}
								</div>
								<div className='space-y-1 leading-none flex items-center gap-1'>
									<RIcon name='Modem02' className='h-4 w-4' />
									<span className='text-sm font-medium select-none'>Répéteur WIFI</span>
								</div>
							</div>
							<div className='flex items-center space-x-2 ml-4 bg-accent/20 rounded-lg p-0.5'>
								<Button
									type='button'
									variant='ghost'
									size='iconSm'
									onClick={(e) => {
										e.stopPropagation();
										handleQuantityChange(false);
									}}
									disabled={repeteurWIFIQuantity <= 0}>
									<Minus size={20} />
								</Button>
								<Input
									type='number'
									value={repeteurWIFIQuantity || 0}
									onChange={(e) => {
										e.stopPropagation();
										handleQuantityInputChange(parseInt(e.target.value) || 0);
									}}
									onClick={(e) => e.stopPropagation()}
									className='w-6 text-center font-bold border-none outline-none focus-visible:ring-0 focus-visible:border-0 bg-transparent p-0 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
									min='0'
								/>
								<Button
									type='button'
									variant='ghost'
									size='iconSm'
									onClick={(e) => {
										e.stopPropagation();
										handleQuantityChange(true);
									}}>
									<Plus size={20} />
								</Button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
